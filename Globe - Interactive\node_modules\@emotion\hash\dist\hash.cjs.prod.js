"use strict";

function murmur2(str) {
  for (var k, h = 0, i = 0, len = str.length; len >= 4; ++i, len -= 4) k = 1540483477 * (65535 & (k = 255 & str.charCodeAt(i) | (255 & str.charCodeAt(++i)) << 8 | (255 & str.charCodeAt(++i)) << 16 | (255 & str.charCodeAt(++i)) << 24)) + (59797 * (k >>> 16) << 16), 
  h = 1540483477 * (65535 & (k ^= k >>> 24)) + (59797 * (k >>> 16) << 16) ^ 1540483477 * (65535 & h) + (59797 * (h >>> 16) << 16);
  switch (len) {
   case 3:
    h ^= (255 & str.charCodeAt(i + 2)) << 16;

   case 2:
    h ^= (255 & str.charCodeAt(i + 1)) << 8;

   case 1:
    h = 1540483477 * (65535 & (h ^= 255 & str.charCodeAt(i))) + (59797 * (h >>> 16) << 16);
  }
  return (((h = 1540483477 * (65535 & (h ^= h >>> 13)) + (59797 * (h >>> 16) << 16)) ^ h >>> 15) >>> 0).toString(36);
}

Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.default = murmur2;
