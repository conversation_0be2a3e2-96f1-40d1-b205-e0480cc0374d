"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "common", {
  enumerable: true,
  get: function get() {
    return _common.default;
  }
});
Object.defineProperty(exports, "red", {
  enumerable: true,
  get: function get() {
    return _red.default;
  }
});
Object.defineProperty(exports, "pink", {
  enumerable: true,
  get: function get() {
    return _pink.default;
  }
});
Object.defineProperty(exports, "purple", {
  enumerable: true,
  get: function get() {
    return _purple.default;
  }
});
Object.defineProperty(exports, "deepPurple", {
  enumerable: true,
  get: function get() {
    return _deepPurple.default;
  }
});
Object.defineProperty(exports, "indigo", {
  enumerable: true,
  get: function get() {
    return _indigo.default;
  }
});
Object.defineProperty(exports, "blue", {
  enumerable: true,
  get: function get() {
    return _blue.default;
  }
});
Object.defineProperty(exports, "lightBlue", {
  enumerable: true,
  get: function get() {
    return _lightBlue.default;
  }
});
Object.defineProperty(exports, "cyan", {
  enumerable: true,
  get: function get() {
    return _cyan.default;
  }
});
Object.defineProperty(exports, "teal", {
  enumerable: true,
  get: function get() {
    return _teal.default;
  }
});
Object.defineProperty(exports, "green", {
  enumerable: true,
  get: function get() {
    return _green.default;
  }
});
Object.defineProperty(exports, "lightGreen", {
  enumerable: true,
  get: function get() {
    return _lightGreen.default;
  }
});
Object.defineProperty(exports, "lime", {
  enumerable: true,
  get: function get() {
    return _lime.default;
  }
});
Object.defineProperty(exports, "yellow", {
  enumerable: true,
  get: function get() {
    return _yellow.default;
  }
});
Object.defineProperty(exports, "amber", {
  enumerable: true,
  get: function get() {
    return _amber.default;
  }
});
Object.defineProperty(exports, "orange", {
  enumerable: true,
  get: function get() {
    return _orange.default;
  }
});
Object.defineProperty(exports, "deepOrange", {
  enumerable: true,
  get: function get() {
    return _deepOrange.default;
  }
});
Object.defineProperty(exports, "brown", {
  enumerable: true,
  get: function get() {
    return _brown.default;
  }
});
Object.defineProperty(exports, "grey", {
  enumerable: true,
  get: function get() {
    return _grey.default;
  }
});
Object.defineProperty(exports, "blueGrey", {
  enumerable: true,
  get: function get() {
    return _blueGrey.default;
  }
});

var _common = _interopRequireDefault(require("./common"));

var _red = _interopRequireDefault(require("./red"));

var _pink = _interopRequireDefault(require("./pink"));

var _purple = _interopRequireDefault(require("./purple"));

var _deepPurple = _interopRequireDefault(require("./deepPurple"));

var _indigo = _interopRequireDefault(require("./indigo"));

var _blue = _interopRequireDefault(require("./blue"));

var _lightBlue = _interopRequireDefault(require("./lightBlue"));

var _cyan = _interopRequireDefault(require("./cyan"));

var _teal = _interopRequireDefault(require("./teal"));

var _green = _interopRequireDefault(require("./green"));

var _lightGreen = _interopRequireDefault(require("./lightGreen"));

var _lime = _interopRequireDefault(require("./lime"));

var _yellow = _interopRequireDefault(require("./yellow"));

var _amber = _interopRequireDefault(require("./amber"));

var _orange = _interopRequireDefault(require("./orange"));

var _deepOrange = _interopRequireDefault(require("./deepOrange"));

var _brown = _interopRequireDefault(require("./brown"));

var _grey = _interopRequireDefault(require("./grey"));

var _blueGrey = _interopRequireDefault(require("./blueGrey"));