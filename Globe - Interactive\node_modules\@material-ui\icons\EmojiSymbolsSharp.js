"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard");

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;

var React = _interopRequireWildcard(require("react"));

var _createSvgIcon = _interopRequireDefault(require("./utils/createSvgIcon"));

var _default = (0, _createSvgIcon.default)( /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement("path", {
  d: "M3 2h8v2H3zM6 11h2V7h3V5H3v2h3zM12.4036 20.1819l7.7781-7.7781 1.4142 1.4142-7.778 7.778z"
}), /*#__PURE__*/React.createElement("circle", {
  cx: "14.5",
  cy: "14.5",
  r: "1.5"
}), /*#__PURE__*/React.createElement("circle", {
  cx: "19.5",
  cy: "19.5",
  r: "1.5"
}), /*#__PURE__*/React.createElement("path", {
  d: "M15.5 11c1.38 0 2.5-1.12 2.5-2.5V4h3V2h-4v4.51c-.42-.32-.93-.51-1.5-.51C14.12 6 13 7.12 13 8.5s1.12 2.5 2.5 2.5zM9.74 15.96l-1.41 1.41-.71-.71.35-.35c.98-.98.98-2.56 0-3.54-.49-.49-1.13-.73-1.77-.73-.64 0-1.28.24-1.77.73-.98.98-.98 2.56 0 3.54l.35.35-1.06 1.06c-.98.98-.98 2.56 0 3.54.5.5 1.14.74 1.78.74s1.28-.24 1.77-.73l1.06-1.06 1.41 1.41 1.41-1.41-1.41-1.41 1.41-1.41-1.41-1.43zM5.85 14.2c.12-.12.26-.15.35-.15s.23.03.35.15c.19.2.19.51 0 .71l-.35.35-.35-.36c-.19-.19-.19-.51 0-.7zm0 5.65c-.12.12-.26.15-.35.15s-.23-.03-.35-.15c-.19-.19-.19-.51 0-.71l1.06-1.06.71.71-1.07 1.06z"
})), 'EmojiSymbolsSharp');

exports.default = _default;